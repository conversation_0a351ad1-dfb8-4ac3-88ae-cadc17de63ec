<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Musik - Jana Breit<PERSON></title>
    <link rel="icon" type="image/png" href="bilder/favicon.png">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="musik-styles.css">
</head>
<body>
    <!-- Header wird durch header-and-footer.js eingefügt -->
    
    <!-- Breadcrumb Navigation -->
    <div class="breadcrumb-container">
        <div class="container">
            <nav class="breadcrumb">
                <a href="index.html" class="breadcrumb-link">Home</a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current">Musik</span>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <main class="musik-main">
        <div class="container">
            <header class="musik-header">
                <h1 class="musik-title">Musik</h1>
            </header>

            <!-- Desktop Layout -->
            <div class="musik-content desktop-layout">
                <!-- Linke Seite: Song-Liste -->
                <div class="song-list-container">
                    <!-- Filter Controls -->
                    <div class="filter-controls">
                        <select id="genre-filter" class="filter-select">
                            <option value="alle">Alle Genres</option>
                        </select>
                        <select id="sprache-filter" class="filter-select">
                            <option value="alle">Alle Sprachen</option>
                        </select>
                        <button id="reset-filters" class="reset-button">Filter zurücksetzen</button>
                    </div>

                    <!-- Song Table -->
                    <div class="song-table-wrapper">
                        <table class="song-table" id="song-table" role="table" aria-label="Musik-Bibliothek">
                            <thead>
                                <tr role="row">
                                    <th class="sortable" data-column="nummer">
                                        #
                                        <span class="sort-indicator"></span>
                                    </th>
                                    <th class="sortable" data-column="titel">
                                        Titel
                                        <span class="sort-indicator"></span>
                                    </th>
                                    <th class="sortable" data-column="album">
                                        Album
                                        <span class="sort-indicator"></span>
                                    </th>
                                    <th class="sortable" data-column="genre">
                                        Genre
                                        <span class="sort-indicator"></span>
                                    </th>
                                    <th class="sortable" data-column="sprache">
                                        Sprache
                                        <span class="sort-indicator"></span>
                                    </th>
                                    <th class="hide-column" data-column="ausblenden">
                                        <button id="toggle-all-visibility" class="header-action-btn" title="Alle ausblenden/einblenden">
                                            <svg class="ear-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M12 2C8 2 5 5 5 9v6c0 1 1 2 2 2h1v-8c0-2.5 2.5-5 5-5s5 2.5 5 5v8h1c1 0 2-1 2-2V9c0-4-3-7-7-7z"/>
                                                <line x1="2" y1="2" x2="22" y2="22" class="strike-line"/>
                                            </svg>
                                        </button>
                                    </th>
                                    <th class="download-column">
                                        <button id="download-all-visible" class="header-action-btn" title="Alle sichtbaren Lieder herunterladen">
                                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                                <polyline points="7,10 12,15 17,10"/>
                                                <line x1="12" y1="15" x2="12" y2="3"/>
                                            </svg>
                                        </button>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="song-table-body">
                                <!-- Songs werden hier dynamisch eingefügt -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Rechte Seite: Player -->
                <div class="player-container">
                    <!-- Album Cover und Song Info -->
                    <div class="player-info">
                        <div class="album-cover-container">
                            <div class="album-cover" id="album-cover">
                                <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="song-info">
                            <h3 class="current-song-title" id="current-song-title">Wähle ein Lied aus</h3>
                            <p class="current-artist" id="current-artist">Jana Breitmar</p>
                            <p class="current-album">Album: <span class="album-link" id="current-album"></span></p>
                        </div>
                    </div>

                    <!-- Player Controls -->
                    <div class="player-controls">
                        <div class="control-buttons">
                            <button class="control-btn" id="prev-btn" title="Vorheriges Lied" aria-label="Vorheriges Lied">
                                <svg viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                                    <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
                                </svg>
                            </button>
                            <button class="control-btn play-pause-btn" id="play-pause-btn" title="Abspielen/Pausieren" aria-label="Abspielen/Pausieren">
                                <svg class="play-icon" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                                    <path d="M8 5v14l11-7z"/>
                                </svg>
                                <svg class="pause-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;" aria-hidden="true">
                                    <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                                </svg>
                            </button>
                            <button class="control-btn" id="next-btn" title="Nächstes Lied" aria-label="Nächstes Lied">
                                <svg viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                                    <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
                                </svg>
                            </button>
                        </div>
                        
                        <div class="progress-container">
                            <span class="time-display" id="current-time">0:00</span>
                            <div class="progress-bar-container">
                                <div class="progress-bar" id="progress-bar">
                                    <div class="progress-fill" id="progress-fill"></div>
                                    <div class="progress-handle" id="progress-handle"></div>
                                </div>
                            </div>
                            <span class="time-display" id="total-time">0:00</span>
                        </div>

                        <div class="additional-controls">
                            <button class="control-btn" id="shuffle-btn" title="Zufällige Wiedergabe">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="16,3 21,3 21,8"/>
                                    <path d="M4 20L21 3"/>
                                    <polyline points="21,16 21,21 16,21"/>
                                    <path d="M15 15l6 6"/>
                                    <path d="M4 4l5 5"/>
                                </svg>
                            </button>
                            <button class="control-btn" id="repeat-btn" title="Wiederholen">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="17,1 21,5 17,9"/>
                                    <path d="M3 11V9a4 4 0 0 1 4-4h14"/>
                                    <polyline points="7,23 3,19 7,15"/>
                                    <path d="M21 13v2a4 4 0 0 1-4 4H3"/>
                                </svg>
                            </button>
                            <button class="control-btn" id="download-current-btn" title="Aktuelles Lied herunterladen">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                    <polyline points="7,10 12,15 17,10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Lyrics -->
                    <div class="lyrics-container">
                        <div class="lyrics-header">
                            <div class="lyrics-tabs" id="lyrics-tabs">
                                <button class="lyrics-tab active" data-tab="lyrics">Lyrics</button>
                                <button class="lyrics-tab" data-tab="translation" style="display: none;">Übersetzung</button>
                                <button class="lyrics-tab" data-tab="info" style="display: none;">Info</button>
                            </div>
                        </div>
                        <div class="lyrics-content" id="lyrics-content">
                            <p class="no-lyrics">Wähle ein Lied aus, um die Lyrics anzuzeigen.</p>
                        </div>
                        <div class="lyrics-content" id="lyrics-translation" style="display: none;">
                            <p class="no-lyrics">Deutsche Übersetzung wird hier angezeigt.</p>
                        </div>
                        <div class="lyrics-content" id="lyrics-info" style="display: none;">
                            <p class="no-lyrics">Zusätzliche Informationen werden hier angezeigt.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile Layout -->
            <div class="mobile-layout">
                <!-- Mobile Filter Controls -->
                <div class="mobile-filter-controls">
                    <select id="mobile-genre-filter" class="filter-select">
                        <option value="alle">Alle Genres</option>
                    </select>
                    <select id="mobile-sprache-filter" class="filter-select">
                        <option value="alle">Alle Sprachen</option>
                    </select>
                    <select id="mobile-album-filter" class="filter-select">
                        <option value="alle">Alle Alben</option>
                    </select>
                </div>

                <!-- Mobile Control Buttons -->
                <div class="mobile-control-buttons">
                    <button class="mobile-control-btn" id="mobile-shuffle-btn" title="Zufällige Wiedergabe">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="16,3 21,3 21,8"/>
                            <path d="M4 20L21 3"/>
                            <polyline points="21,16 21,21 16,21"/>
                            <path d="M15 15l6 6"/>
                            <path d="M4 4l5 5"/>
                        </svg>
                    </button>
                    <button class="mobile-control-btn" id="mobile-repeat-btn" title="Wiederholen">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="17,1 21,5 17,9"/>
                            <path d="M3 11V9a4 4 0 0 1 4-4h14"/>
                            <polyline points="7,23 3,19 7,15"/>
                            <path d="M21 13v2a4 4 0 0 1-4 4H3"/>
                        </svg>
                    </button>
                    <button class="mobile-control-btn" id="mobile-reset-filters" title="Filter zurücksetzen">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="3,6 5,6 21,6"/>
                            <path d="M19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                            <line x1="10" y1="11" x2="10" y2="17"/>
                            <line x1="14" y1="11" x2="14" y2="17"/>
                        </svg>
                    </button>
                </div>

                <!-- Mobile Song List -->
                <div class="mobile-song-list" id="mobile-song-list">
                    <!-- Songs werden hier dynamisch eingefügt -->
                </div>

                <!-- Mini Player (wird am unteren Rand angezeigt) -->
                <div class="mini-player" id="mini-player" style="display: none;">
                    <div class="mini-player-info" id="mini-player-info">
                        <div class="mini-album-cover">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                            </svg>
                        </div>
                        <div class="mini-song-info">
                            <span class="mini-song-title" id="mini-song-title">Kein Lied ausgewählt</span>
                            <span class="mini-artist">Jana Breitmar</span>
                        </div>
                    </div>
                    <div class="mini-controls">
                        <button class="mini-control-btn" id="mini-play-pause" title="Abspielen/Pausieren">
                            <svg class="play-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M8 5v14l11-7z"/>
                            </svg>
                            <svg class="pause-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                            </svg>
                        </button>
                        <button class="mini-control-btn" id="mini-expand" title="Player öffnen">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="15,3 21,3 21,9"/>
                                <polyline points="9,21 3,21 3,15"/>
                                <line x1="21" y1="3" x2="14" y2="10"/>
                                <line x1="3" y1="21" x2="10" y2="14"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Mobile Player Modal -->
    <div class="modal-overlay" id="modal-overlay" style="display: none;">
        <div class="modal-player" id="modal-player">
            <div class="modal-header">
                <button class="modal-close" id="modal-close">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-content">
                <!-- Player content wird hier eingefügt -->
            </div>
        </div>
    </div>

    <!-- Audio Element -->
    <audio id="audio-player" preload="metadata"></audio>

    <!-- Scripts -->
    <script src="header-and-footer.js"></script>
    <script src="musik-data.js"></script>
    <script src="musik-player.js"></script>
</body>
</html>
